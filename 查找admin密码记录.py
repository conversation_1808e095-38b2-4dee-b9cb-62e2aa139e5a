#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找项目中所有记录admin密码的地方
"""

import os
import re
from pathlib import Path

def search_admin_password():
    """搜索项目中所有可能记录admin密码的地方"""
    print("=" * 60)
    print("查找项目中admin密码记录")
    print("=" * 60)
    
    project_root = Path(__file__).parent
    
    # 要搜索的文件类型
    file_patterns = [
        "*.md", "*.txt", "*.py", "*.kt", "*.java", "*.yml", "*.yaml", 
        "*.properties", "*.json", "*.log", "*.sql", "*.sh", "*.bat"
    ]
    
    # 要搜索的密码相关关键词
    password_patterns = [
        r"admin123",
        r"password.*admin",
        r"admin.*password",
        r"密码.*admin",
        r"admin.*密码",
        r"🔑.*密码",
        r"logger\.info.*密码",
        r"logger\.info.*password"
    ]
    
    found_files = []
    
    print("\n🔍 搜索文件中...")
    
    for pattern in file_patterns:
        for file_path in project_root.rglob(pattern):
            # 跳过一些不需要搜索的目录
            if any(skip in str(file_path) for skip in [
                'node_modules', '.git', 'build', 'dist', 'target', 
                '__pycache__', '.gradle', 'venv', 'env'
            ]):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    for i, pwd_pattern in enumerate(password_patterns):
                        matches = re.finditer(pwd_pattern, content, re.IGNORECASE)
                        for match in matches:
                            # 获取匹配行的行号
                            line_num = content[:match.start()].count('\n') + 1
                            line_content = content.split('\n')[line_num - 1].strip()
                            
                            found_files.append({
                                'file': str(file_path.relative_to(project_root)),
                                'line': line_num,
                                'content': line_content,
                                'pattern': pwd_pattern
                            })
                            
            except Exception as e:
                # 忽略无法读取的文件
                pass
    
    # 输出结果
    if found_files:
        print(f"\n📋 找到 {len(found_files)} 个可能的密码记录:")
        print("-" * 60)
        
        current_file = None
        for item in found_files:
            if item['file'] != current_file:
                current_file = item['file']
                print(f"\n📁 {item['file']}")
            
            print(f"   第{item['line']}行: {item['content']}")
            
        print("\n" + "=" * 60)
        print("📝 总结:")
        
        # 按文件分组统计
        file_counts = {}
        for item in found_files:
            file_counts[item['file']] = file_counts.get(item['file'], 0) + 1
        
        for file_name, count in file_counts.items():
            print(f"   {file_name}: {count} 处")
            
    else:
        print("\n✅ 未找到明显的密码记录")
    
    print("\n" + "=" * 60)
    print("🔍 特别检查:")
    
    # 检查特定的重要文件
    important_files = [
        "项目启动手册.md",
        "upbm/src/main/kotlin/com/gditcommon/upbm/core/server/manager/DefaultService.kt",
        "upbm/src/main/resources/application.yml",
        "README.md"
    ]
    
    for file_name in important_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"✅ {file_name} - 存在")
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if 'admin123' in content.lower():
                        print(f"   ⚠️  包含 'admin123'")
                    if '密码' in content or 'password' in content.lower():
                        print(f"   📝 包含密码相关内容")
            except:
                pass
        else:
            print(f"❌ {file_name} - 不存在")
    
    print("\n" + "=" * 60)
    print("💡 建议:")
    print("1. 检查 DefaultService.kt 中的日志输出")
    print("2. 启动后端服务查看控制台日志")
    print("3. 如果忘记密码，运行 'python 重置管理员密码.py'")
    print("4. 更新文档中的默认密码信息")

def check_current_admin_status():
    """检查当前admin用户状态"""
    print("\n🔍 检查当前admin用户状态...")
    
    # 检查数据库连接
    mysql_paths = [
        r"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe",
        r"C:\Program Files\MySQL\MySQL Server 8.4\bin\mysql.exe",
        "mysql"
    ]
    
    for mysql_path in mysql_paths:
        try:
            import subprocess
            cmd = f'"{mysql_path}" -u root -p123456 -e "USE testupbm; SELECT COUNT(*) as user_count FROM user WHERE user_name = \'admin\';" 2>nul'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and 'user_count' in result.stdout:
                if '1' in result.stdout:
                    print("✅ admin用户已存在于数据库中")
                    print("💡 密码应该在后端服务启动时的日志中显示")
                else:
                    print("❌ admin用户不存在于数据库中")
                    print("💡 需要启动后端服务来创建admin用户")
                return
        except Exception as e:
            continue
    
    print("⚠️  无法连接到MySQL数据库")
    print("💡 请确保MySQL服务正在运行")

if __name__ == "__main__":
    try:
        search_admin_password()
        check_current_admin_status()
    except KeyboardInterrupt:
        print("\n\n❌ 搜索被用户中断")
    except Exception as e:
        print(f"\n❌ 搜索过程出错: {e}")
