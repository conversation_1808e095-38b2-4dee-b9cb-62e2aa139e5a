# 输电线路设备缺陷识别系统启动手册

## 系统架构
- **前端**: React + Vite (uloi)
- **后端**: Spring Boot + <PERSON><PERSON><PERSON> (upbm) 
- **AI服务**: Flask + YOLOv8 (pules)
- **数据库**: MySQL
- **缓存**: Redis

## 环境要求
- Java 17+
- Node.js 16+
- Python 3.11+
- MySQL 8.0+
- Redis 6.0+
- Gradle 8.3+

## 快速启动

### 1. 环境检查
```bash
python 快速环境检查.py
```

### 2. 启动数据库服务
```bash
# 启动MySQL (Windows服务)
net start mysql80

# 启动Redis
net start redis
```

### 3. 启动后端服务 (端口18084)
```bash
cd upbm
gradle bootRun
```

### 4. 启动前端服务 (端口5173)
```bash
cd uloi
npm run dev
```

### 5. 启动AI识别服务 (端口5000)
```bash
cd pules
conda activate tel
python main.py
```

## 访问地址
- **前端界面**: http://localhost:5173
- **后端API**: http://localhost:18084
- **AI识别API**: http://127.0.0.1:5000

## 默认账户
- **用户名**: admin
- **密码**: admin123

## 一键启动
```bash
启动系统.bat
```

## 服务状态检查
```bash
python 服务状态监控.py
```

## 故障排除

### 端口冲突
- 前端: 修改vite.config.js中的port
- 后端: 修改application.yml中的server.port
- AI服务: 修改main.py中的port参数

### 数据库连接失败
1. 检查MySQL服务状态
2. 验证用户名密码 (root/123456)
3. 确认数据库testupbm存在

### Redis连接失败
```bash
net start redis
```

### 环境配置问题
```bash
python 环境检查和自动配置.py
```

## 开发模式
- 前端热重载: 自动启用
- 后端热重载: Spring Boot DevTools
- AI服务调试: debug=True

## 生产部署
参考: `bushucankao/docker-compose.yml`
