# Flask项目启动手册

## 环境要求
- Python 3.11+
- Anaconda/Miniconda

## 快速启动

### 1. 激活环境
```bash
conda activate tel
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
pip install watchdog
```

### 3. 启动应用
```bash
python main.py
```

### 4. 访问地址
```
http://127.0.0.1:5000
```

## API接口

### 图像检测
- **URL**: `/detect`
- **方法**: `POST`
- **请求体**:
```json
{
    "base64": "图片的base64编码"
}
```
- **响应**:
```json
{
    "base64": "处理后图片的base64编码",
    "birdNestCount": 鸟巢数量,
    "insulatorCount": 绝缘子数量,
    "shockHammerCount": 减震器数量
}
```

## 测试脚本
```bash
python debug.py
```

## 故障排除

### 启动失败
1. 检查Python版本: `python --version`
2. 检查环境: `conda info --envs`
3. 重新安装依赖: `pip install -r requirements.txt`

### 导入错误
```bash
pip install watchdog
```

### 端口占用
修改main.py中的端口:
```python
app.run(debug=True, port=5001)
```
