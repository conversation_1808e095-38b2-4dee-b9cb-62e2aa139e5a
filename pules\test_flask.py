#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用测试脚本 - 用于诊断watchdog导入错误
"""

from flask import Flask, jsonify
import sys
import traceback

app = Flask(__name__)

@app.route('/')
def hello():
    return jsonify({
        'message': 'Flask应用运行正常',
        'python_version': sys.version,
        'debug_mode': app.debug
    })

@app.route('/test')
def test():
    return jsonify({'status': 'success', 'message': '测试端点正常工作'})

if __name__ == '__main__':
    print("🚀 开始启动Flask应用...")
    print(f"Python版本: {sys.version}")
    
    try:
        print("📦 检查Flask版本...")
        import flask
        print(f"Flask版本: {flask.__version__}")
        
        print("📦 检查Werkzeug版本...")
        import werkzeug
        try:
            print(f"Werkzeug版本: {werkzeug.__version__}")
        except AttributeError:
            # 新版本的werkzeug可能使用不同的方式获取版本
            try:
                from werkzeug import __version__ as werkzeug_version
                print(f"Werkzeug版本: {werkzeug_version}")
            except ImportError:
                import pkg_resources
                werkzeug_version = pkg_resources.get_distribution("werkzeug").version
                print(f"Werkzeug版本: {werkzeug_version}")
        
        print("📦 检查watchdog包...")
        try:
            import watchdog
            print(f"Watchdog版本: {watchdog.__version__}")
            
            # 尝试导入可能有问题的模块
            from watchdog.events import EVENT_TYPE_OPENED
            print("✅ EVENT_TYPE_OPENED导入成功")
        except ImportError as e:
            print(f"❌ Watchdog导入错误: {e}")
            print("这可能是导致Flask debug模式失败的原因")
        
        print("🔧 尝试启动Flask应用...")
        app.run(debug=True, host='127.0.0.1', port=5000)
        
    except Exception as e:
        print(f"❌ Flask启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
